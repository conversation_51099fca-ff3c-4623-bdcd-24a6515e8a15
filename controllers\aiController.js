const { GoogleGenerativeAI } = require('@google/generative-ai');
const { pool } = require('../config/db');
require('dotenv').config();

/**
 * Rate limiting constants for AI requests
 */
const RATE_LIMITS = {
    ACTIVE_SUBSCRIPTION: 10,  // Daily limit for users with active subscription
    NO_SUBSCRIPTION: 2        // Daily limit for users without active subscription
};

/**
 * Error messages in Persian for rate limiting
 */
const ERROR_MESSAGES = {
    SUBSCRIPTION_LIMIT_EXCEEDED: "سهمیه روزانه شما برای استفاده از هوش مصنوعی به پایان رسیده است.",
    NO_SUBSCRIPTION_LIMIT_EXCEEDED: "برای استفاده بیشتر از هوش مصنوعی، لطفا اشتراک تهیه کنید."
};

const getAiFeedback = async (req, res) => {
  try {
    // Check if API key is configured
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey || apiKey === 'YOUR_API_KEY_HERE') {
      return res.status(500).json({
        error: 'Gemini API key not configured. Please set GEMINI_API_KEY in your .env file.'
      });
    }

    // Get user text and mobile from request body
    const { user_text, mobile } = req.body;
    if (!user_text) {
      return res.status(400).json({
        error: 'user_text is required in request body'
      });
    }

    if (!mobile) {
      return res.status(400).json({
        error: 'mobile is required in request body for rate limiting'
      });
    }

    // Database operations: Check user subscription and rate limits
    const client = await pool.connect();

    try {
      // Begin transaction for atomic operations
      await client.query('BEGIN');

      // Get user data with subscription status
      const userQuery = `
        SELECT
          id,
          mobile_number,
          subscription_expires_at,
          ai_requests_today,
          last_ai_request_date,
          (subscription_expires_at IS NOT NULL AND subscription_expires_at > NOW()) as has_active_subscription
        FROM users
        WHERE mobile_number = $1
      `;

      const userResult = await client.query(userQuery, [mobile]);

      if (userResult.rows.length === 0) {
        await client.query('ROLLBACK');
        console.warn('⚠️ User not found for mobile:', mobile ? `${mobile.substring(0, 3)}***${mobile.substring(mobile.length - 2)}` : 'undefined');
        return res.status(404).json({
          error: 'User not found. Please register first.'
        });
      }

      const user = userResult.rows[0];
      console.log('👤 AI request from user:', {
        id: user.id,
        mobile: `${user.mobile_number.substring(0, 3)}***${user.mobile_number.substring(user.mobile_number.length - 2)}`,
        hasActiveSubscription: user.has_active_subscription,
        currentRequests: user.ai_requests_today,
        lastRequestDate: user.last_ai_request_date
      });

      // Check if we need to reset daily counter (new day)
      const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
      const lastRequestDate = user.last_ai_request_date ? user.last_ai_request_date.toISOString().split('T')[0] : null;

      let currentRequestCount = user.ai_requests_today;

      if (lastRequestDate !== today) {
        // New day - reset counter
        currentRequestCount = 0;
        console.log('📅 New day detected, resetting AI request counter');
      }

      // Determine rate limit based on subscription status
      const dailyLimit = user.has_active_subscription ? RATE_LIMITS.ACTIVE_SUBSCRIPTION : RATE_LIMITS.NO_SUBSCRIPTION;
      const errorMessage = user.has_active_subscription ?
        ERROR_MESSAGES.SUBSCRIPTION_LIMIT_EXCEEDED :
        ERROR_MESSAGES.NO_SUBSCRIPTION_LIMIT_EXCEEDED;

      console.log('🔍 Rate limit check:', {
        currentRequests: currentRequestCount,
        dailyLimit: dailyLimit,
        hasActiveSubscription: user.has_active_subscription
      });

      // Check if user has exceeded daily limit
      if (currentRequestCount >= dailyLimit) {
        await client.query('ROLLBACK');
        console.warn('🚫 Rate limit exceeded:', {
          userId: user.id,
          currentRequests: currentRequestCount,
          dailyLimit: dailyLimit,
          hasActiveSubscription: user.has_active_subscription
        });

        return res.status(429).json({
          error: errorMessage,
          details: {
            currentRequests: currentRequestCount,
            dailyLimit: dailyLimit,
            hasActiveSubscription: user.has_active_subscription
          }
        });
      }

      // Increment request counter and update last request date
      const updateQuery = `
        UPDATE users
        SET
          ai_requests_today = $1,
          last_ai_request_date = CURRENT_DATE
        WHERE id = $2
      `;

      await client.query(updateQuery, [currentRequestCount + 1, user.id]);
      await client.query('COMMIT');

      console.log('✅ Rate limit check passed, proceeding with AI request:', {
        userId: user.id,
        newRequestCount: currentRequestCount + 1,
        dailyLimit: dailyLimit
      });

    } catch (dbError) {
      await client.query('ROLLBACK');
      console.error('❌ Database error in AI rate limiting:', dbError.message);
      return res.status(500).json({
        error: 'Database error occurred. Please try again.'
      });
    } finally {
      client.release();
    }

    // Configure Google Generative AI client
    const genAI = new GoogleGenerativeAI(apiKey);
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash-latest' });

    // Create the prompt for the AI
    const prompt = `You are a creative writing coach. Analyze the following text for coherence, sentence structure, and creativity, and provide constructive feedback in Persian. Text: ${user_text}`;

    // Generate content using Gemini
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const feedback = response.text();

    console.log('🤖 AI feedback generated successfully for user:', mobile ? `${mobile.substring(0, 3)}***${mobile.substring(mobile.length - 2)}` : 'undefined');

    // Send the feedback back to the client
    res.json({ feedback });

  } catch (error) {
    console.error('❌ Error getting AI feedback:', error);
    res.status(500).json({
      error: 'Failed to get AI feedback. Please try again later.'
    });
  }
};

module.exports = {
  getAiFeedback
};
