-- =====================================================
-- Database Migration: Subscription Model Implementation
-- =====================================================
-- This migration transforms the users table from a one-time purchase model
-- to a subscription-based model with rate limiting capabilities.
--
-- Changes:
-- 1. Remove the existing 'role' column
-- 2. Add subscription tracking columns
-- 3. Add AI usage tracking columns
-- 4. Add performance indexes
-- =====================================================

-- Start transaction to ensure atomicity
BEGIN;

-- Log migration start
DO $$
BEGIN
    RAISE NOTICE 'Starting subscription model migration at %', NOW();
END $$;

-- =====================================================
-- STEP 1: Remove existing role column (if exists)
-- =====================================================
DO $$
BEGIN
    -- Check if role column exists and drop it
    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name = 'role'
    ) THEN
        ALTER TABLE users DROP COLUMN role;
        RAISE NOTICE 'Dropped existing role column';
    ELSE
        RAISE NOTICE 'Role column does not exist, skipping drop';
    END IF;
END $$;

-- =====================================================
-- STEP 2: Add subscription tracking columns
-- =====================================================

-- Add subscription_expires_at column (nullable TIMESTAMPTZ)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name = 'subscription_expires_at'
    ) THEN
        ALTER TABLE users ADD COLUMN subscription_expires_at TIMESTAMPTZ;
        RAISE NOTICE 'Added subscription_expires_at column';
    ELSE
        RAISE NOTICE 'subscription_expires_at column already exists';
    END IF;
END $$;

-- Add had_active_subscription column (BOOLEAN with DEFAULT false)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name = 'had_active_subscription'
    ) THEN
        ALTER TABLE users ADD COLUMN had_active_subscription BOOLEAN DEFAULT false;
        RAISE NOTICE 'Added had_active_subscription column';
    ELSE
        RAISE NOTICE 'had_active_subscription column already exists';
    END IF;
END $$;

-- =====================================================
-- STEP 3: Add AI usage tracking columns
-- =====================================================

-- Add ai_requests_today column (INTEGER with DEFAULT 0)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name = 'ai_requests_today'
    ) THEN
        ALTER TABLE users ADD COLUMN ai_requests_today INTEGER DEFAULT 0;
        RAISE NOTICE 'Added ai_requests_today column';
    ELSE
        RAISE NOTICE 'ai_requests_today column already exists';
    END IF;
END $$;

-- Add last_ai_request_date column (nullable DATE)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name = 'last_ai_request_date'
    ) THEN
        ALTER TABLE users ADD COLUMN last_ai_request_date DATE;
        RAISE NOTICE 'Added last_ai_request_date column';
    ELSE
        RAISE NOTICE 'last_ai_request_date column already exists';
    END IF;
END $$;

-- =====================================================
-- STEP 4: Add performance indexes
-- =====================================================

-- Index for subscription status queries (most frequently used)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM pg_indexes 
        WHERE tablename = 'users' 
        AND indexname = 'idx_users_subscription_expires_at'
    ) THEN
        CREATE INDEX idx_users_subscription_expires_at ON users(subscription_expires_at);
        RAISE NOTICE 'Created index on subscription_expires_at';
    ELSE
        RAISE NOTICE 'Index on subscription_expires_at already exists';
    END IF;
END $$;

-- Index for AI usage tracking queries
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM pg_indexes 
        WHERE tablename = 'users' 
        AND indexname = 'idx_users_last_ai_request_date'
    ) THEN
        CREATE INDEX idx_users_last_ai_request_date ON users(last_ai_request_date);
        RAISE NOTICE 'Created index on last_ai_request_date';
    ELSE
        RAISE NOTICE 'Index on last_ai_request_date already exists';
    END IF;
END $$;

-- Composite index for subscription and AI usage queries
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM pg_indexes 
        WHERE tablename = 'users' 
        AND indexname = 'idx_users_subscription_ai_usage'
    ) THEN
        CREATE INDEX idx_users_subscription_ai_usage ON users(subscription_expires_at, last_ai_request_date, ai_requests_today);
        RAISE NOTICE 'Created composite index for subscription and AI usage';
    ELSE
        RAISE NOTICE 'Composite index for subscription and AI usage already exists';
    END IF;
END $$;

-- =====================================================
-- STEP 5: Data migration for existing users
-- =====================================================

-- Initialize new columns for existing users
UPDATE users 
SET 
    had_active_subscription = false,
    ai_requests_today = 0,
    last_ai_request_date = NULL,
    subscription_expires_at = NULL
WHERE 
    had_active_subscription IS NULL 
    OR ai_requests_today IS NULL;

-- Get count of updated users
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Initialized subscription data for % existing users', updated_count;
END $$;

-- =====================================================
-- STEP 6: Add constraints and validation
-- =====================================================

-- Add check constraint to ensure ai_requests_today is non-negative
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.check_constraints 
        WHERE constraint_name = 'chk_users_ai_requests_today_non_negative'
    ) THEN
        ALTER TABLE users ADD CONSTRAINT chk_users_ai_requests_today_non_negative 
        CHECK (ai_requests_today >= 0);
        RAISE NOTICE 'Added check constraint for ai_requests_today';
    ELSE
        RAISE NOTICE 'Check constraint for ai_requests_today already exists';
    END IF;
END $$;

-- =====================================================
-- STEP 7: Create helper functions for subscription management
-- =====================================================

-- Function to check if user has active subscription
CREATE OR REPLACE FUNCTION is_subscription_active(user_subscription_expires_at TIMESTAMPTZ)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN user_subscription_expires_at IS NOT NULL AND user_subscription_expires_at > NOW();
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to get daily AI request limit based on subscription status
CREATE OR REPLACE FUNCTION get_daily_ai_limit(user_subscription_expires_at TIMESTAMPTZ)
RETURNS INTEGER AS $$
BEGIN
    IF is_subscription_active(user_subscription_expires_at) THEN
        RETURN 10; -- Active subscription limit
    ELSE
        RETURN 2;  -- Free tier limit
    END IF;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- =====================================================
-- STEP 8: Create view for subscription status
-- =====================================================

-- Create or replace view for easy subscription status queries
CREATE OR REPLACE VIEW user_subscription_status AS
SELECT 
    id,
    mobile_number,
    subscription_expires_at,
    had_active_subscription,
    ai_requests_today,
    last_ai_request_date,
    is_subscription_active(subscription_expires_at) as has_active_subscription,
    get_daily_ai_limit(subscription_expires_at) as daily_ai_limit,
    CASE 
        WHEN is_subscription_active(subscription_expires_at) THEN 'active'
        WHEN had_active_subscription THEN 'expired'
        ELSE 'never_subscribed'
    END as subscription_status
FROM users;

-- =====================================================
-- STEP 9: Final validation and commit
-- =====================================================

-- Validate the migration by checking table structure
DO $$
DECLARE
    column_count INTEGER;
    index_count INTEGER;
BEGIN
    -- Check that all required columns exist
    SELECT COUNT(*) INTO column_count
    FROM information_schema.columns 
    WHERE table_name = 'users' 
    AND column_name IN (
        'subscription_expires_at', 
        'had_active_subscription', 
        'ai_requests_today', 
        'last_ai_request_date'
    );
    
    IF column_count != 4 THEN
        RAISE EXCEPTION 'Migration validation failed: Expected 4 new columns, found %', column_count;
    END IF;
    
    -- Check that indexes were created
    SELECT COUNT(*) INTO index_count
    FROM pg_indexes 
    WHERE tablename = 'users' 
    AND indexname IN (
        'idx_users_subscription_expires_at',
        'idx_users_last_ai_request_date',
        'idx_users_subscription_ai_usage'
    );
    
    IF index_count != 3 THEN
        RAISE EXCEPTION 'Migration validation failed: Expected 3 new indexes, found %', index_count;
    END IF;
    
    RAISE NOTICE 'Migration validation successful: % columns and % indexes created', column_count, index_count;
END $$;

-- Log migration completion
DO $$
BEGIN
    RAISE NOTICE 'Subscription model migration completed successfully at %', NOW();
    RAISE NOTICE 'New columns: subscription_expires_at, had_active_subscription, ai_requests_today, last_ai_request_date';
    RAISE NOTICE 'New indexes: idx_users_subscription_expires_at, idx_users_last_ai_request_date, idx_users_subscription_ai_usage';
    RAISE NOTICE 'Helper functions: is_subscription_active(), get_daily_ai_limit()';
    RAISE NOTICE 'New view: user_subscription_status';
END $$;

-- Commit the transaction
COMMIT;

-- =====================================================
-- Migration Complete
-- =====================================================
