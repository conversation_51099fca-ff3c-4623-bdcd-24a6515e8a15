// @ts-check
const axios = require('axios');
const NodeRSA = require('node-rsa');
const { pool } = require('../config/db');

/**
 * SKU to subscription duration mapping (in months)
 * Add new subscription SKUs here as they become available
 */
const subscriptionDurations = {
    '3_month_sub': 3,    // 3 months subscription
    '6_month_sub': 6,    // 6 months subscription
    '12_month_sub': 12,  // 12 months subscription
    '1_month_sub': 1,    // 1 month subscription
    // Add more SKUs as needed
};

/**
 * Verifies Myket in-app purchase and updates user subscription in database
 * This controller handles the complete flow of subscription purchase verification including:
 * 1. Validating purchase data with Myket's official API
 * 2. Verifying cryptographic signature for security
 * 3. Updating user subscription status in database upon successful verification
 * 4. Calculating subscription expiration dates based on SKU
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Promise<void>}
 */
const verifyMyketPurchase = async (req, res) => {
    try {
        // Extract required fields from request body
        const { packageName, sku, purchaseToken, mobile } = req.body;
        
        // Log received data for debugging (excluding sensitive information)
        console.log('📱 Myket Purchase Verification Request:', {
            packageName,
            sku,
            mobile: mobile ? `${mobile.substring(0, 3)}***${mobile.substring(mobile.length - 2)}` : 'undefined',
            purchaseTokenLength: purchaseToken ? purchaseToken.length : 0,
            timestamp: new Date().toISOString()
        });

        // Validate required fields
        if (!packageName || !sku || !purchaseToken || !mobile) {
            console.warn('⚠️ Missing required fields in purchase verification request');
            return res.status(400).json({
                success: false,
                message: "Missing required fields: packageName, sku, purchaseToken, and mobile are required."
            });
        }

        // Validate SKU exists in subscription durations mapping
        if (!subscriptionDurations.hasOwnProperty(sku)) {
            console.warn('⚠️ Invalid SKU provided:', sku);
            return res.status(400).json({
                success: false,
                message: `Invalid subscription SKU: ${sku}. Supported SKUs: ${Object.keys(subscriptionDurations).join(', ')}`
            });
        }

        const subscriptionMonths = subscriptionDurations[sku];
        console.log(`📅 Processing ${subscriptionMonths}-month subscription for SKU: ${sku}`);

        // Construct Myket API verification URL
        const verificationUrl = `https://developer.myket.ir/api/applications/${packageName}/purchases/products/${sku}/tokens/${purchaseToken}`;
        console.log('🔍 Verifying purchase with Myket API:', verificationUrl);

        // TODO: Replace with actual long-lived access token from Myket developer console
        const myketAccessToken = process.env.MYKET_ACCESS_TOKEN || 'YOUR_MYKET_ACCESS_TOKEN';
        
        if (myketAccessToken === 'YOUR_MYKET_ACCESS_TOKEN') {
            console.error('❌ MYKET_ACCESS_TOKEN environment variable not configured');
            return res.status(500).json({
                success: false,
                message: "Server configuration error. Please contact support."
            });
        }

        // Make verification request to Myket API
        let myketResponse;
        try {
            myketResponse = await axios.get(verificationUrl, {
                headers: {
                    'Authorization': `Bearer ${myketAccessToken}`,
                    'Content-Type': 'application/json',
                    'User-Agent': 'MyketPurchaseVerifier/1.0'
                },
                timeout: 10000 // 10 second timeout
            });
        } catch (apiError) {
            console.error('❌ Myket API verification failed:', {
                status: apiError.response?.status,
                statusText: apiError.response?.statusText,
                message: apiError.message
            });
            
            return res.status(400).json({
                success: false,
                message: "Invalid purchase data."
            });
        }

        // Check if Myket API returned successful response
        if (myketResponse.status !== 200) {
            console.warn('⚠️ Myket API returned non-200 status:', myketResponse.status);
            return res.status(400).json({
                success: false,
                message: "Invalid purchase data."
            });
        }

        console.log('✅ Myket API verification successful');

        // Extract signature from Myket response for verification
        const { signature } = myketResponse.data;
        
        if (!signature) {
            console.error('❌ No signature found in Myket API response');
            return res.status(400).json({
                success: false,
                message: "Invalid purchase data."
            });
        }

        // Load RSA public key from environment variable
        const publicKeyPem = process.env.MYKET_RSA_PUBLIC_KEY;
        
        if (!publicKeyPem) {
            console.error('❌ MYKET_RSA_PUBLIC_KEY environment variable not configured');
            return res.status(500).json({
                success: false,
                message: "Server configuration error. Please contact support."
            });
        }

        // Verify RSA signature for security
        try {
            const publicKey = new NodeRSA(publicKeyPem);
            const isSignatureValid = publicKey.verify(
                purchaseToken,           // Data to verify
                signature,               // Signature from Myket
                'utf8',                  // Source encoding
                'base64'                 // Signature encoding
            );

            if (!isSignatureValid) {
                console.warn('🔒 RSA signature verification failed - potential security threat', {
                    mobile: mobile ? `${mobile.substring(0, 3)}***${mobile.substring(mobile.length - 2)}` : 'undefined',
                    packageName,
                    sku,
                    timestamp: new Date().toISOString()
                });
                
                return res.status(400).json({
                    success: false,
                    message: "Signature verification failed."
                });
            }

            console.log('🔒 RSA signature verification successful');
        } catch (signatureError) {
            console.error('❌ RSA signature verification error:', signatureError.message);
            return res.status(400).json({
                success: false,
                message: "Signature verification failed."
            });
        }

        // Database operations: Find user and update subscription
        const client = await pool.connect();
        let updatedExpiration; // Declare in outer scope for response

        try {
            // Begin transaction for data consistency
            await client.query('BEGIN');

            // Find user by mobile number with current subscription status
            const userQuery = `
                SELECT
                    id,
                    mobile_number,
                    subscription_expires_at,
                    had_active_subscription,
                    ai_requests_today,
                    last_ai_request_date
                FROM users
                WHERE mobile_number = $1
            `;
            const userResult = await client.query(userQuery, [mobile]);

            if (userResult.rows.length === 0) {
                await client.query('ROLLBACK');
                console.warn('⚠️ User not found for mobile:', mobile ? `${mobile.substring(0, 3)}***${mobile.substring(mobile.length - 2)}` : 'undefined');
                return res.status(404).json({
                    success: false,
                    message: "User not found."
                });
            }

            const user = userResult.rows[0];
            console.log('👤 User found:', {
                id: user.id,
                mobile: `${user.mobile_number.substring(0, 3)}***${user.mobile_number.substring(user.mobile_number.length - 2)}`,
                currentSubscriptionExpires: user.subscription_expires_at,
                hadActiveSubscription: user.had_active_subscription
            });

            // Calculate new subscription expiration date
            let newExpirationDate;
            const now = new Date();

            if (user.subscription_expires_at && new Date(user.subscription_expires_at) > now) {
                // User has active subscription - extend from current expiration
                newExpirationDate = new Date(user.subscription_expires_at);
                newExpirationDate.setMonth(newExpirationDate.getMonth() + subscriptionMonths);
                console.log(`📅 Extending existing subscription by ${subscriptionMonths} months`);
            } else {
                // User has no active subscription - start from now
                newExpirationDate = new Date(now);
                newExpirationDate.setMonth(newExpirationDate.getMonth() + subscriptionMonths);
                console.log(`📅 Creating new ${subscriptionMonths}-month subscription`);
            }

            // Update user subscription data
            const updateQuery = `
                UPDATE users
                SET
                    subscription_expires_at = $1,
                    had_active_subscription = true,
                    ai_requests_today = 0,
                    last_ai_request_date = CURRENT_DATE
                WHERE id = $2
                RETURNING subscription_expires_at
            `;

            const updateResult = await client.query(updateQuery, [newExpirationDate, user.id]);
            updatedExpiration = updateResult.rows[0].subscription_expires_at;

            // Commit transaction
            await client.query('COMMIT');

            console.log('✅ User subscription updated successfully:', {
                userId: user.id,
                newExpirationDate: updatedExpiration,
                subscriptionMonths: subscriptionMonths,
                timestamp: new Date().toISOString()
            });

        } catch (dbError) {
            // Rollback transaction on error
            await client.query('ROLLBACK');
            console.error('❌ Database operation failed:', dbError.message);

            return res.status(500).json({
                success: false,
                message: "Database error occurred. Please try again."
            });
        } finally {
            // Always release the database client
            client.release();
        }

        // Return success response with subscription details
        return res.status(200).json({
            success: true,
            message: "Subscription purchase verified successfully.",
            data: {
                subscriptionExpiresAt: updatedExpiration,
                subscriptionMonths: subscriptionMonths,
                sku: sku
            }
        });

    } catch (error) {
        // Catch any unexpected errors
        console.error('❌ Unexpected error in verifyMyketPurchase:', error.message);
        console.error('Stack trace:', error.stack);
        
        return res.status(500).json({
            success: false,
            message: "An unexpected error occurred. Please try again."
        });
    }
};

// Export the controller function
module.exports = {
    verifyMyketPurchase
};
