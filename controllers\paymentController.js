// @ts-check
const axios = require('axios');
const NodeRSA = require('node-rsa');
const { pool } = require('../config/db');

/**
 * Verifies Myket in-app purchase and updates user role in database
 * This controller handles the complete flow of purchase verification including:
 * 1. Validating purchase data with Myket's official API
 * 2. Verifying cryptographic signature for security
 * 3. Updating user role in database upon successful verification
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Promise<void>}
 */
const verifyMyketPurchase = async (req, res) => {
    try {
        // Extract required fields from request body
        const { packageName, sku, purchaseToken, mobile } = req.body;
        
        // Log received data for debugging (excluding sensitive information)
        console.log('📱 Myket Purchase Verification Request:', {
            packageName,
            sku,
            mobile: mobile ? `${mobile.substring(0, 3)}***${mobile.substring(mobile.length - 2)}` : 'undefined',
            purchaseTokenLength: purchaseToken ? purchaseToken.length : 0,
            timestamp: new Date().toISOString()
        });

        // Validate required fields
        if (!packageName || !sku || !purchaseToken || !mobile) {
            console.warn('⚠️ Missing required fields in purchase verification request');
            return res.status(400).json({
                success: false,
                message: "Missing required fields: packageName, sku, purchaseToken, and mobile are required."
            });
        }

        // Construct Myket API verification URL
        const verificationUrl = `https://developer.myket.ir/api/applications/${packageName}/purchases/products/${sku}/tokens/${purchaseToken}`;
        console.log('🔍 Verifying purchase with Myket API:', verificationUrl);

        // TODO: Replace with actual long-lived access token from Myket developer console
        const myketAccessToken = process.env.MYKET_ACCESS_TOKEN || 'YOUR_MYKET_ACCESS_TOKEN';
        
        if (myketAccessToken === 'YOUR_MYKET_ACCESS_TOKEN') {
            console.error('❌ MYKET_ACCESS_TOKEN environment variable not configured');
            return res.status(500).json({
                success: false,
                message: "Server configuration error. Please contact support."
            });
        }

        // Make verification request to Myket API
        let myketResponse;
        try {
            myketResponse = await axios.get(verificationUrl, {
                headers: {
                    'Authorization': `Bearer ${myketAccessToken}`,
                    'Content-Type': 'application/json',
                    'User-Agent': 'MyketPurchaseVerifier/1.0'
                },
                timeout: 10000 // 10 second timeout
            });
        } catch (apiError) {
            console.error('❌ Myket API verification failed:', {
                status: apiError.response?.status,
                statusText: apiError.response?.statusText,
                message: apiError.message
            });
            
            return res.status(400).json({
                success: false,
                message: "Invalid purchase data."
            });
        }

        // Check if Myket API returned successful response
        if (myketResponse.status !== 200) {
            console.warn('⚠️ Myket API returned non-200 status:', myketResponse.status);
            return res.status(400).json({
                success: false,
                message: "Invalid purchase data."
            });
        }

        console.log('✅ Myket API verification successful');

        // Extract signature from Myket response for verification
        const { signature } = myketResponse.data;
        
        if (!signature) {
            console.error('❌ No signature found in Myket API response');
            return res.status(400).json({
                success: false,
                message: "Invalid purchase data."
            });
        }

        // Load RSA public key from environment variable
        const publicKeyPem = process.env.MYKET_RSA_PUBLIC_KEY;
        
        if (!publicKeyPem) {
            console.error('❌ MYKET_RSA_PUBLIC_KEY environment variable not configured');
            return res.status(500).json({
                success: false,
                message: "Server configuration error. Please contact support."
            });
        }

        // Verify RSA signature for security
        try {
            const publicKey = new NodeRSA(publicKeyPem);
            const isSignatureValid = publicKey.verify(
                purchaseToken,           // Data to verify
                signature,               // Signature from Myket
                'utf8',                  // Source encoding
                'base64'                 // Signature encoding
            );

            if (!isSignatureValid) {
                console.warn('🔒 RSA signature verification failed - potential security threat', {
                    mobile: mobile ? `${mobile.substring(0, 3)}***${mobile.substring(mobile.length - 2)}` : 'undefined',
                    packageName,
                    sku,
                    timestamp: new Date().toISOString()
                });
                
                return res.status(400).json({
                    success: false,
                    message: "Signature verification failed."
                });
            }

            console.log('🔒 RSA signature verification successful');
        } catch (signatureError) {
            console.error('❌ RSA signature verification error:', signatureError.message);
            return res.status(400).json({
                success: false,
                message: "Signature verification failed."
            });
        }

        // Database operations: Find user and update role
        const client = await pool.connect();
        
        try {
            // Begin transaction for data consistency
            await client.query('BEGIN');

            // Find user by mobile number
            const userQuery = 'SELECT id, mobile, role FROM users WHERE mobile = $1';
            const userResult = await client.query(userQuery, [mobile]);

            if (userResult.rows.length === 0) {
                await client.query('ROLLBACK');
                console.warn('⚠️ User not found for mobile:', mobile ? `${mobile.substring(0, 3)}***${mobile.substring(mobile.length - 2)}` : 'undefined');
                return res.status(404).json({
                    success: false,
                    message: "User not found."
                });
            }

            const user = userResult.rows[0];
            console.log('👤 User found:', {
                id: user.id,
                mobile: `${user.mobile.substring(0, 3)}***${user.mobile.substring(user.mobile.length - 2)}`,
                currentRole: user.role
            });

            // Update user role to 'buyer'
            const updateQuery = 'UPDATE users SET role = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2';
            await client.query(updateQuery, ['buyer', user.id]);

            // Commit transaction
            await client.query('COMMIT');
            
            console.log('✅ User role updated successfully:', {
                userId: user.id,
                newRole: 'buyer',
                timestamp: new Date().toISOString()
            });

        } catch (dbError) {
            // Rollback transaction on error
            await client.query('ROLLBACK');
            console.error('❌ Database operation failed:', dbError.message);
            
            return res.status(500).json({
                success: false,
                message: "Database error occurred. Please try again."
            });
        } finally {
            // Always release the database client
            client.release();
        }

        // Return success response
        return res.status(200).json({
            success: true,
            message: "Purchase verified successfully. User role updated."
        });

    } catch (error) {
        // Catch any unexpected errors
        console.error('❌ Unexpected error in verifyMyketPurchase:', error.message);
        console.error('Stack trace:', error.stack);
        
        return res.status(500).json({
            success: false,
            message: "An unexpected error occurred. Please try again."
        });
    }
};

// Export the controller function
module.exports = {
    verifyMyketPurchase
};
